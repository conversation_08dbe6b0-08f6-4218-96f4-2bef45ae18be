/* eslint-disable max-len */
/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {Button} from '@panda-design/components';
import {MCPSquareLink, MCPPlaygroundLink} from '@/links/mcp';
import {IconAiTools1, IconAiTools2, IconAiTools3, IconAiTools4, IconArrowRight} from '@/icons/mcp';
import mcpAIToolsBg from '@/assets/mcp/mcpAIToolsBg.png';

const Container = styled.div`
    width: 100%;
    min-height: calc(100vh - 48px);
    position: relative;
    background-image: url(${mcpAIToolsBg});
    background-repeat: no-repeat;
    background-position: top center;
    overflow: hidden;
`;

const TopNavArea = styled(Flex)`
    position: absolute;
    top: 20px;
    right: 56px;
`;

const NavLink = styled.a`
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #181818;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    padding: 5px 16px;

    svg {
        margin-left: 8px;
    }
`;

const MainTitleContainer = styled.div`
    position: absolute;
    margin-top: 64px;
    margin-left: 56px;
`;

const MainTitle = styled.div`
    font-family: 'HarmonyOS Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    font-weight: 900;
    font-size: 46px;
    line-height: 50px;
    letter-spacing: 1px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-synthesis: weight;
    background: linear-gradient(265.61deg, #65CFFF -5.04%, #0072F8 46.76%, #001C85 75.2%, #000D56 101.78%);
    background-clip: text;
    color: transparent;
`;

const SubTitle = styled.div`
    font-weight: 700;
    font-size: 32px;
    line-height: 50px;
    letter-spacing: 11px;
    background: linear-gradient(265.61deg, #65CFFF -5.04%, #0072F8 46.76%, #001C85 75.2%, #000D56 101.78%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
`;

const Description = styled.div`
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #8D8D8D;
    margin: 16px 0 0 0;
`;

const ButtonArea = styled(Flex)`
    margin-top: 40px;
    gap: 16px;
`;

const FeaturesContainer = styled(Flex)`
    margin-top: 359px;
    margin-left: 56px;
    margin-right: 56px;
    justify-content: space-between;
`;

const FeatureCard = styled.div`
    flex: 1;
    height: 144px;
    border: 2px solid #FFFFFF;
    border-radius: 8px;
    padding: 24px 16px;
    background: #fff;
    margin-right: 16px;
    position: relative;
    background: linear-gradient(178.56deg, rgba(204, 229, 255, 0.8) -44.84%, rgba(230, 242, 255, 0.8) -5.04%, rgba(242, 249, 255, 0.8) 24.57%, rgba(255, 255, 255, 0.8) 56.11%);
    backdrop-filter: blur(40px);
    box-shadow: 0px 0px 8px 0px #1B1B1B1A;

    &:last-child {
        margin-right: 0;
    }

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
`;

const FeatureTitle = styled.div`
    font-weight: 500;
    font-size: 20px;
    line-height: 36px;
    color: #181818;
    margin-bottom: 8px;
`;

const FeatureContent = styled.div`
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #8F8F8F;
`;

const ProcessContainer = styled(Flex)`
    margin: 40px 56px 0 56px;
    gap: 16px;
`;

const ProcessCard = styled.div`
    flex: 1;
    border-radius: 8px;
    box-shadow: 0px 0px 8px 0px #00000014;
`;

const ProcessHeader = styled(Flex)`
    width: 100%;
    height: 66px;
    background: linear-gradient(88.32deg, #E5F2FF 1.61%, rgba(255, 254, 240, 0.8) 99.57%);
    align-items: center;
    justify-content: space-between;
    border-radius: 8px 8px 0 0;
    position: relative;
`;

const ProcessTitle = styled.h3`
    font-size: 16px;
    font-weight: 600;
    color: #000;
    margin-left: 62px;
`;

const ProcessSteps = styled.div`
    padding: 24px;
`;

const ProcessStep = styled.div`
    display: flex;
    margin-bottom: 20px;
    position: relative;

    &:last-child {
        margin-bottom: 0;
    }

    &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 7px;
        top: 24px;
        width: 0.5px;
        height: calc(100% - 8px);
        border-left: 0.5px dashed #BFBFBF;
        border-left-style: dashed;
        border-left-width: 0.5px;
    }
`;

const StepIndicator = styled.div`
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #D9D9D9;
    margin-right: 16px;
    margin-top: 2px;
    flex-shrink: 0;
`;

const StepContent = styled.div`
    flex: 1;
`;

const StepTitle = styled.h4`
    font-size: 14px;
    font-weight: 600;
    color: #000;
    margin: 0 0 8px 0;
`;

const StepDescription = styled.p`
    font-size: 14px;
    color: #666;
    line-height: 20px;
    margin: 0;
`;

const IconWrapper = styled.div`
    position: absolute;
    right: 16px;
    top: -16px;
`;


const ProcessDetailButton = styled(Button)`
    position: absolute;
    top: 50%;
    right: 40px;
    transform: translateY(-50%);
    padding: 5px 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
`;

const features = [
    {
        title: '降低双边成本',
        content: '为供需双方降本，实现开发、接入、调用成本优化',
    },
    {
        title: '协议无缝转换',
        content: '统一转换层，支持MCP、OpenAPI、脚本快速转换',
    },
    {
        title: '工具精准调用',
        content: '提供多维调试入口，帮助用户发现、高效调用工具',
    },
    {
        title: '构建完整生态',
        content: '打造服务到编排的闭环，赋能开发者与业务方创新',
    },
];

const producerSteps = [
    {
        title: '便捷注册',
        description: '快速将MCP或OpenAPI封装为MCP Server，完成接入',
    },
    {
        title: '灵活配置',
        description: '自定义工具参数与模板，确保模型能高效调用',
    },
    {
        title: '在线调试',
        description: '配置后可在线调试验证，查看返回结果，确保可靠',
    },
    {
        title: '发布共享',
        description: '将MCP Server发布到广场，分享生态提升服务价值',
    },
];

const consumerSteps = [
    {
        title: '广场发现',
        description: '在广场多维度探索筛选MCP，精准匹配业务需求',
    },
    {
        title: '组合试用',
        description: '在Playground组合试用，与大模型交互评估效果',
    },
    {
        title: '一键订阅',
        description: '按需创建应用并订阅MCP，灵活按需配置工具',
    },
    {
        title: '集成调用',
        description: '将已订阅工具轻松集成至AI Agent，即刻扩展能力',
    },
];

const AIToolsHome = () => {
    return (
        <Container>
            <TopNavArea>
                <NavLink href="#">使用文档<IconArrowRight /></NavLink>
                <NavLink href="#">介绍文档<IconArrowRight /></NavLink>
            </TopNavArea>

            <MainTitleContainer>
                <MainTitle>万千工具一网打尽</MainTitle>
                <SubTitle>智能体从此有求必应</SubTitle>
                <Description>
                    厂内MCP服务中心：一站式解决AI Agent工具生产与调用难题
                </Description>
                <ButtonArea>
                    <MCPSquareLink>
                        <Button type="primary">去MCP广场体验</Button>
                    </MCPSquareLink>
                    <MCPPlaygroundLink>
                        <Button>去Playground试用</Button>
                    </MCPPlaygroundLink>
                </ButtonArea>
            </MainTitleContainer>

            <FeaturesContainer>
                {features.map((feature, index) => (
                    <FeatureCard key={index}>
                        <FeatureTitle>{feature.title}</FeatureTitle>
                        <FeatureContent>{feature.content}</FeatureContent>
                        {index === 0 && (
                            <IconWrapper>
                                <IconAiTools1 style={{fontSize: 56}} />
                            </IconWrapper>
                        )}
                        {index === 1 && (
                            <IconWrapper>
                                <IconAiTools2 style={{fontSize: 56}} />
                            </IconWrapper>
                        )}
                        {index === 2 && (
                            <IconWrapper>
                                <IconAiTools3 style={{fontSize: 56}} />
                            </IconWrapper>
                        )}
                        {index === 3 && (
                            <IconWrapper>
                                <IconAiTools4 style={{fontSize: 56}} />
                            </IconWrapper>
                        )}
                    </FeatureCard>
                ))}
            </FeaturesContainer>

            <ProcessContainer>
                <ProcessCard>
                    <ProcessHeader>
                        <ProcessTitle>工具生产者使用流程</ProcessTitle>
                        <ProcessDetailButton type="text" size="small">
                            查看详情<IconArrowRight />
                        </ProcessDetailButton>
                    </ProcessHeader>
                    <ProcessSteps>
                        {producerSteps.map((step, index) => (
                            <ProcessStep key={index}>
                                <StepIndicator />
                                <StepContent>
                                    <StepTitle>{step.title}</StepTitle>
                                    <StepDescription>{step.description}</StepDescription>
                                </StepContent>
                            </ProcessStep>
                        ))}
                    </ProcessSteps>
                </ProcessCard>

                <ProcessCard>
                    <ProcessHeader>
                        <ProcessTitle>工具消费者使用流程</ProcessTitle>
                        <ProcessDetailButton type="text" size="small">
                            查看详情<IconArrowRight />
                        </ProcessDetailButton>
                    </ProcessHeader>
                    <ProcessSteps>
                        {consumerSteps.map((step, index) => (
                            <ProcessStep key={index}>
                                <StepIndicator />
                                <StepContent>
                                    <StepTitle>{step.title}</StepTitle>
                                    <StepDescription>{step.description}</StepDescription>
                                </StepContent>
                            </ProcessStep>
                        ))}
                    </ProcessSteps>
                </ProcessCard>
            </ProcessContainer>
        </Container>
    );
};

export default AIToolsHome;
